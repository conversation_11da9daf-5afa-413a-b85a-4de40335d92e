# Supabase Database Setup Guide

This guide explains how the LayerEdge $Edgen Community application is configured to use Supabase PostgreSQL database.

## Database Configuration

### Current Setup

The application is configured with the following Supabase database:

- **Database Identifier**: `bzqayhnlogpaxfcmmrlq`
- **Region**: `aws-0-eu-north-1` (EU North - Stockholm)
- **Password**: `d234A879a1#`

### Connection Strings

#### For Application (Transaction Pooler)
```
DATABASE_URL="postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1"
```

#### For Migrations (Session Pooler)
```
DIRECT_URL="postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:5432/postgres"
```

### Key Features

1. **IPv4 Compatibility**: Uses pooler endpoints that support both IPv4 and IPv6, ensuring compatibility with Koyeb deployment
2. **Connection Pooling**: Optimized for serverless environments with transaction pooling
3. **Prepared Statements**: Disabled for transaction pooler compatibility using `pgbouncer=true&connection_limit=1`
4. **Dual URLs**: Separate URLs for application operations and database migrations

## Prisma Configuration

### Schema Configuration

The Prisma schema is configured with:

```prisma
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}
```

### Migration Strategy

- **Development**: Use `prisma migrate dev` for creating and applying migrations
- **Production**: Use `prisma migrate deploy` for applying migrations (included in build script)

## Environment Variables

### Required Variables

Set these environment variables in your deployment platform (Koyeb):

```bash
# Database - Supabase PostgreSQL
DATABASE_URL="postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1"
DIRECT_URL="postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:5432/postgres"
```

### Local Development

For local development, the same connection strings are used in `.env` and `.env.local` files.

## Database Schema

The application uses the following main models:

### User Model
- Stores user profiles with authentication data
- Tracks points and rankings
- Links to Twitter/X accounts

### Tweet Model
- Stores submitted tweets with engagement metrics
- Calculates points based on engagement
- Links to users

### PointsHistory Model
- Tracks all point awards over time
- Provides audit trail for point calculations

### Authentication Models
- Account, Session, VerificationToken (NextAuth.js compatible)

## Deployment Considerations

### Koyeb Deployment

1. **Build Process**: Migrations run automatically during build via `prisma migrate deploy`
2. **Environment Variables**: Set both `DATABASE_URL` and `DIRECT_URL` in Koyeb
3. **IPv4 Support**: Uses pooler endpoints for maximum compatibility

### Performance Optimization

1. **Connection Pooling**: Transaction pooler handles multiple concurrent connections
2. **Query Optimization**: Indexes on frequently queried fields
3. **Prepared Statements**: Disabled for pooler compatibility

## Troubleshooting

### Common Issues

1. **Prepared Statement Errors**: Ensure `pgbouncer=true&connection_limit=1` is in DATABASE_URL
2. **Connection Timeouts**: Verify pooler endpoints are accessible
3. **Migration Failures**: Use DIRECT_URL for migration operations

### Verification Steps

1. Test connection: `npx prisma db push`
2. Run migrations: `npx prisma migrate deploy`
3. Seed database: `npm run db:seed`
4. Check data: `npx prisma studio`

## Security Notes

1. **Password Encoding**: Special characters are URL-encoded (`#` becomes `%23`)
2. **Environment Variables**: Never commit database credentials to version control
3. **Access Control**: Database access is restricted to application and authorized users
4. **SSL**: All connections use SSL encryption

## Monitoring and Maintenance

1. **Connection Monitoring**: Monitor connection pool usage in Supabase dashboard
2. **Query Performance**: Use Supabase query analyzer for optimization
3. **Backup Strategy**: Supabase provides automated backups
4. **Scaling**: Connection pooling handles traffic spikes automatically
