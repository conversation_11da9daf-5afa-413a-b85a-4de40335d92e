<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LayerEdge Styles Test</title>
    <style>
        :root {
            /* LayerEdge Brand Colors */
            --background: #0a0a0a;
            --foreground: #ffffff;
            --primary: #f59e0b;
            --primary-foreground: #000000;
            --secondary: #1f2937;
            --secondary-foreground: #e5e7eb;
            --accent: #3b82f6;
            --accent-foreground: #ffffff;
            --muted: #374151;
            --muted-foreground: #9ca3af;
            --border: #2d3748;
            --input: #1a202c;
            --ring: #f59e0b;
            --card: #111827;
            --card-foreground: #f9fafb;
            
            /* LayerEdge specific colors */
            --layeredge-orange: #f59e0b;
            --layeredge-orange-light: #fbbf24;
            --layeredge-orange-dark: #d97706;
            --layeredge-blue: #3b82f6;
            --layeredge-grid: #1a202c;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: system-ui, sans-serif;
            padding: 2rem;
            margin: 0;
        }

        /* LayerEdge Gradient Text */
        .text-layeredge-gradient {
            background: linear-gradient(135deg, var(--layeredge-orange) 0%, var(--layeredge-orange-light) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* LayerEdge Button Styles */
        .btn-layeredge-primary {
            background: linear-gradient(135deg, var(--layeredge-orange) 0%, var(--layeredge-orange-light) 100%);
            color: #000000;
            font-weight: 600;
            transition: all 0.2s ease-in-out;
            padding: 12px 24px;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }

        .btn-layeredge-primary:hover {
            background: linear-gradient(135deg, var(--layeredge-orange-dark) 0%, var(--layeredge-orange) 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.2);
        }

        /* LayerEdge Card Styles */
        .card-layeredge {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            transition: all 0.3s ease-in-out;
            padding: 24px;
            margin: 16px 0;
        }

        .card-layeredge:hover {
            border-color: var(--layeredge-orange);
            box-shadow: 0 8px 32px rgba(245, 158, 11, 0.1);
            transform: translateY(-2px);
        }

        /* Grid Pattern */
        .bg-grid-pattern {
            background-image: 
                linear-gradient(rgba(245, 158, 11, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(245, 158, 11, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            padding: 40px;
            border-radius: 12px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1, h2 {
            margin-bottom: 1rem;
        }

        .logo-demo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 20px 0;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--layeredge-orange) 0%, var(--layeredge-orange-light) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #000000;
        }

        .points-badge {
            background: rgba(245, 158, 11, 0.1);
            color: var(--layeredge-orange);
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-layeredge-gradient">LayerEdge Community Platform</h1>
        <p>Testing the new LayerEdge brand styling</p>

        <div class="logo-demo">
            <div class="logo-icon">LE</div>
            <div>
                <div style="font-size: 20px; font-weight: bold;">LayerEdge</div>
                <div class="text-layeredge-gradient" style="font-size: 14px; font-weight: 600;">$Edgen Community</div>
            </div>
        </div>

        <h2>Buttons</h2>
        <button class="btn-layeredge-primary">Join Community</button>
        <button class="btn-layeredge-primary">Sign in with X</button>

        <h2>Cards</h2>
        <div class="card-layeredge">
            <h3>Sample Tweet Card</h3>
            <p>This is how tweet cards will look with the new LayerEdge styling.</p>
            <div class="points-badge">125 points</div>
        </div>

        <div class="card-layeredge">
            <h3>Feature Card</h3>
            <p>Feature cards now have LayerEdge branding with orange hover effects.</p>
        </div>

        <h2>Grid Pattern Background</h2>
        <div class="bg-grid-pattern">
            <h3 class="text-layeredge-gradient">Bitcoin-Backed Internet</h3>
            <p>This section demonstrates the LayerEdge grid pattern background that matches their official website aesthetic.</p>
        </div>

        <h2>Typography</h2>
        <h1 class="text-layeredge-gradient">Large Gradient Heading</h1>
        <h2 class="text-layeredge-gradient">Medium Gradient Heading</h2>
        <p>Regular text maintains excellent readability while the gradient text provides brand consistency.</p>
    </div>
</body>
</html>
