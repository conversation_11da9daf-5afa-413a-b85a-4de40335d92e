# LayerEdge Community Platform - Automatic Tweet Monitoring

## Overview

The LayerEdge community platform now features **automatic Twitter/X tweet monitoring** that eliminates the need for manual tweet submissions. Users simply connect their X account and the system automatically tracks their tweets containing `@layeredge` or `$EDGEN` mentions.

## Key Features

### 🤖 Automatic Tweet Discovery
- **Real-time monitoring** of user tweets
- **Smart filtering** for @layeredge and $EDGEN mentions
- **Background processing** with rate limit handling
- **Duplicate prevention** to avoid double-counting

### 📊 Automatic Points Calculation
- **Base points**: 5 points per qualifying tweet
- **Engagement bonuses**: 
  - 1 point per like
  - 3 points per retweet
  - 2 points per reply
- **Real-time updates** as engagement grows

### 🔄 Seamless User Experience
- **One-time setup**: Connect X account and monitoring starts automatically
- **No manual submissions**: Users just tweet normally
- **Dashboard integration**: View automatically tracked tweets
- **Monitoring status**: Real-time status and controls

## How It Works

### 1. User Authentication
```typescript
// Enhanced OAuth with monitoring initialization
TwitterProvider({
  clientId: process.env.TWITTER_CLIENT_ID!,
  clientSecret: process.env.TWITTER_CLIENT_SECRET!,
  version: '2.0',
  authorization: {
    url: 'https://twitter.com/i/oauth2/authorize',
    params: {
      scope: 'users.read tweet.read offline.access',
    },
  },
})
```

### 2. Automatic Monitoring Setup
When users sign in, the system automatically:
- Creates a `TweetMonitoring` record
- Enables automatic monitoring by default
- Initializes background tracking

### 3. Tweet Discovery Process
```typescript
// Search for user tweets with LayerEdge mentions
const query = `from:${username} (@layeredge OR $EDGEN)`
const tweets = await searchUserTweets(username, sinceId)
```

### 4. Content Validation
```typescript
// Validate tweet content
function validateTweetContent(content: string): boolean {
  const normalizedContent = content.toLowerCase()
  return normalizedContent.includes('@layeredge') || 
         normalizedContent.includes('$edgen')
}
```

### 5. Points Calculation & Database Updates
```typescript
// Calculate points and update database
const totalPoints = calculatePoints(likes, retweets, replies)
await createTweetRecord(tweetData, totalPoints)
await updateUserPoints(userId, totalPoints)
```

## API Endpoints

### User Monitoring
- `POST /api/monitoring/user` - Trigger manual check for current user
- `GET /api/monitoring/user` - Get monitoring status for current user

### Settings Management
- `POST /api/monitoring/settings` - Enable/disable automatic monitoring
- `GET /api/monitoring/settings` - Get current monitoring settings

### Batch Operations
- `POST /api/monitoring/batch` - Run monitoring for all users (cron job)
- `GET /api/monitoring/batch` - Get monitoring statistics (admin)

### Cron Job
- `GET /api/cron/monitor-tweets` - Scheduled monitoring endpoint

## Database Schema

### Enhanced User Model
```prisma
model User {
  // ... existing fields
  autoMonitoringEnabled Boolean @default(true)
  lastTweetCheck        DateTime?
  tweetCheckCount       Int @default(0)
  tweetMonitoring       TweetMonitoring[]
}
```

### Tweet Monitoring
```prisma
model TweetMonitoring {
  id           String   @id @default(cuid())
  userId       String
  lastCheckAt  DateTime @default(now())
  tweetsFound  Int      @default(0)
  status       String   @default("active")
  errorMessage String?
  user         User     @relation(fields: [userId], references: [id])
  @@unique([userId])
}
```

### Enhanced Tweet Model
```prisma
model Tweet {
  // ... existing fields
  tweetId          String?  // Twitter's internal ID
  discoveredAt     DateTime? // Auto-discovery timestamp
  isAutoDiscovered Boolean @default(false)
}
```

## Monitoring Service

### TwitterMonitoringService
The core service handles:
- **User tweet search** via Twitter API
- **Content validation** for required mentions
- **Duplicate detection** and prevention
- **Points calculation** and database updates
- **Error handling** and rate limit management

### Key Methods
```typescript
class TwitterMonitoringService {
  // Search for user tweets with LayerEdge mentions
  async searchUserTweets(username: string, sinceId?: string)
  
  // Process discovered tweets and add to database
  async processDiscoveredTweets(userId: string, tweets: TwitterTimelineResponse)
  
  // Monitor tweets for a specific user
  async monitorUserTweets(userId: string)
  
  // Monitor tweets for all active users
  async monitorAllUsers()
}
```

## Deployment & Scheduling

### Koyeb Cron Jobs
Set up scheduled monitoring with Koyeb cron:
```bash
# Every 30 minutes
0,30 * * * * curl -H "Authorization: Bearer ${CRON_SECRET}" https://edgen.koyeb.app/api/cron/monitor-tweets
```

### Environment Variables
```bash
# Twitter API
TWITTER_CLIENT_ID=your_client_id
TWITTER_CLIENT_SECRET=your_client_secret
TWITTER_BEARER_TOKEN=your_bearer_token

# Cron Security
CRON_SECRET=layeredge-cron-secret-2024
ADMIN_SECRET=layeredge-admin-secret-2024
```

## User Interface Changes

### Removed Features
- ❌ Manual tweet submission page (`/submit`)
- ❌ "Submit Tweet" navigation link
- ❌ Quick actions for manual submission

### New Features
- ✅ Automatic monitoring status display
- ✅ Real-time monitoring controls
- ✅ Manual check trigger button
- ✅ Monitoring settings toggle
- ✅ Auto-discovery indicators

### Dashboard Updates
- **Monitoring Status Card**: Shows current monitoring state
- **Auto-Discovery Badges**: Indicates automatically found tweets
- **Real-time Controls**: Manual check and settings buttons
- **Status Indicators**: Visual feedback for monitoring health

## Rate Limiting & Error Handling

### Twitter API Limits
- **Search API**: 300 requests per 15 minutes
- **Batch processing**: 5 users per batch with delays
- **Exponential backoff**: For rate limit recovery
- **Fallback mechanisms**: Web scraping when API fails

### Error Recovery
- **Automatic retries** with exponential backoff
- **Status tracking** in TweetMonitoring table
- **Error logging** for debugging
- **Graceful degradation** when services fail

## Testing

### Manual Testing
```bash
# Run monitoring test script
npx tsx scripts/test-monitoring.ts
```

### API Testing
```bash
# Test user monitoring
curl -X POST http://localhost:3000/api/monitoring/user \
  -H "Cookie: next-auth.session-token=your_token"

# Test batch monitoring
curl -X POST http://localhost:3000/api/monitoring/batch \
  -H "Authorization: Bearer layeredge-cron-secret-2024"
```

## Migration Guide

### From Manual to Automatic
1. **Database migration** applied automatically
2. **Existing users** get monitoring enabled by default
3. **Historical tweets** remain unchanged
4. **New tweets** are automatically discovered

### User Communication
- **Dashboard notifications** about the new system
- **Help text** explaining automatic tracking
- **Settings page** for user control

## Security Considerations

### API Security
- **Cron endpoint protection** with secret tokens
- **User authentication** for all monitoring endpoints
- **Rate limiting** to prevent abuse
- **Input validation** for all parameters

### Privacy
- **User consent** through monitoring settings
- **Data minimization** - only store necessary tweet data
- **Secure token handling** for Twitter API access
- **GDPR compliance** with user data controls

## Future Enhancements

### Planned Features
- **Real-time webhooks** for instant tweet detection
- **Advanced analytics** for engagement trends
- **Community challenges** based on automatic tracking
- **Mobile app integration** with push notifications

### Performance Optimizations
- **Caching layer** for frequently accessed data
- **Database indexing** for faster queries
- **Background job queues** for better scalability
- **CDN integration** for global performance
