# LayerEdge Fallback System - Testing Guide

## Overview

This guide explains how to test the LayerEdge fallback system that provides automatic switching between Twitter API and web scraping to handle rate limiting issues.

## Test Script

The main test script is `test-fallback-system.mjs` - an ES module compatible with Next.js 15's module system.

### Running the Test Script

```bash
# Make sure your development server is running
npm run dev

# In a separate terminal, run the test script
node test-fallback-system.mjs
```

### Test Script Features

The test script (`test-fallback-system.mjs`) includes:

1. **Health Check** - Verifies the fallback service is running
2. **Fallback Status** - Checks current API/scraper preference
3. **Tweet Data Fetching** - Tests auto, API, and scraper methods
4. **Engagement Metrics** - Tests single and batch engagement updates
5. **Error Handling** - Validates proper error responses

### ES Module Compatibility

The test script has been updated for ES module compatibility:

- ✅ Uses `import` statements instead of `require()`
- ✅ Uses ES6 `export` syntax instead of `module.exports`
- ✅ Proper main module detection with `import.meta.url`
- ✅ Compatible with Next.js 15's `"type": "module"` configuration

### Configuration

Before running tests, update the `TEST_TWEETS` array in `test-fallback-system.mjs` with real LayerEdge community tweet URLs:

```javascript
const TEST_TWEETS = [
  'https://x.com/your_tweet_url_1',
  'https://x.com/your_tweet_url_2',
]
```

### Environment Variables

Ensure your `.env.local` file includes:

```env
# Required for testing
TWITTER_BEARER_TOKEN=your_bearer_token
ENABLE_WEB_SCRAPING=true

# Optional test configuration
BASE_URL=http://localhost:3000
```

## Manual Testing

### 1. Health Check

```bash
curl -I http://localhost:3000/api/scrape/engagement
```

Expected headers:
- `X-Service-Health: ok`
- `X-Fallback-Status: {...}`
- `X-Preferred-Source: api|scraper`

### 2. Tweet Data Fetching

```bash
# Auto method (intelligent fallback)
curl "http://localhost:3000/api/scrape/tweets?url=TWEET_URL"

# Force API method
curl "http://localhost:3000/api/scrape/tweets?url=TWEET_URL&method=api"

# Force scraper method
curl "http://localhost:3000/api/scrape/tweets?url=TWEET_URL&method=scraper"
```

### 3. Engagement Metrics

```bash
# Single tweet engagement
curl -X POST http://localhost:3000/api/scrape/engagement \
  -H "Content-Type: application/json" \
  -d '{"tweetUrl": "TWEET_URL"}'

# Batch engagement
curl -X POST http://localhost:3000/api/scrape/engagement \
  -H "Content-Type: application/json" \
  -d '{"tweetUrls": ["TWEET_URL_1", "TWEET_URL_2"]}'
```

## Testing Scenarios

### Rate Limiting Simulation

To test the fallback behavior during rate limiting:

1. Make multiple rapid API requests to trigger rate limiting
2. Observe automatic switch to scraping method
3. Verify cooldown period and recovery

### Error Handling

Test various error scenarios:

- Invalid tweet URLs
- Non-existent tweets
- Network timeouts
- Scraping failures

### Performance Testing

Monitor performance metrics:

- API response times
- Scraping duration
- Memory usage during browser operations
- Batch processing efficiency

## Expected Results

### Successful Test Output

```
🚀 Testing LayerEdge Fallback System
=====================================

📊 Test 1: Health Check
   Health check status: 200
   Service health: ok
   Fallback status: {"preferredSource":"api","apiFailureCount":0}
✅ Health check passed

📊 Test 2: Fallback Status
   Preferred source: api
   API failures: 0
   Rate limited: false
✅ Fallback status check passed

📊 Test 3: Tweet Data Fetching (Auto)
   Testing auto method for: https://x.com/...
   Response status: 200
   ✅ Data source: api
   ✅ Tweet content: "This is a test tweet..."
   ✅ Engagement: 10 likes, 5 retweets
✅ Auto tweet fetching passed

🎉 All tests completed successfully!
```

### Error Scenarios

The test script handles various error scenarios gracefully:

- Missing test URLs (skips tests with warnings)
- API failures (shows fallback status)
- Network errors (displays error details)
- Invalid responses (logs debugging information)

## Troubleshooting

### Common Issues

1. **ES Module Errors**
   - Ensure you're using `node test-fallback-system.mjs` (not `.js`)
   - Verify Next.js 15 is properly configured with `"type": "module"`

2. **Test Failures**
   - Check if development server is running (`npm run dev`)
   - Verify environment variables are set
   - Update TEST_TWEETS with valid URLs

3. **Scraping Issues**
   - Ensure Playwright browsers are installed (`npx playwright install`)
   - Check memory availability for browser instances
   - Verify network connectivity

4. **API Rate Limiting**
   - This is expected behavior - tests should show fallback to scraping
   - Wait for cooldown period or test scraper-only mode

### Debug Mode

For detailed debugging, set environment variables:

```bash
LOG_LEVEL=debug node test-fallback-system.mjs
```

## Integration with CI/CD

The test script can be integrated into automated testing pipelines:

```yaml
# Example GitHub Actions step
- name: Test Fallback System
  run: |
    npm run dev &
    sleep 10  # Wait for server to start
    node test-fallback-system.mjs
  env:
    TWITTER_BEARER_TOKEN: ${{ secrets.TWITTER_BEARER_TOKEN }}
    ENABLE_WEB_SCRAPING: true
```

## Next Steps

After successful testing:

1. Deploy to staging environment
2. Run tests against staging
3. Monitor fallback behavior in production
4. Set up alerts for fallback status changes

For more detailed information, see `md/FALLBACK_SYSTEM_IMPLEMENTATION.md`.
